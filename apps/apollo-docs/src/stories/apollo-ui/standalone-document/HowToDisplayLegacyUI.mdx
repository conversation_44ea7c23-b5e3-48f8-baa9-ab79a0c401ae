import React from "react"
import { Typography } from "@apollo/ui"
import { Meta } from "@storybook/addon-docs/blocks"
import { Container, FileText, Safety } from "@design-systems/apollo-icons"

<Meta title="@apollo∕ui/How to Display Legacy UI" tags={["docs"]} />

<div
  style={{
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    textAlign: "center",
    gap: 16,
    padding: "16px 0 32px",
  }}
>
  <Typography
    align="center"
    level="displayMedium"
    style={{ margin: "0px", color: "#121212" }}
  >How to Display Legacy UI</Typography>
  <Typography
    align="center"
    level="titleLarge"
    style={{ color: "var(--sb-secondary-text-color)" }}
  >Manage CSS specificity when displaying legacy UI components alongside Apollo UI using CSS @layer</Typography>
  <div
    style={{
      display: "flex",
      gap: 8,
      flexWrap: "wrap",
      justifyContent: "center",
      padding: "32px",
      minWidth: "460px",
    }}
  >
    <Container size={64} style={{ color: "#0154EE" }} />
  </div>
</div>

## Overview

When migrating to Apollo UI, you often need to display legacy UI components alongside new Apollo components during the transition period. This can lead to CSS conflicts and specificity issues that break the visual consistency of your application.

CSS `@layer` provides a powerful solution for managing these conflicts by explicitly controlling the cascade order of your styles, regardless of specificity or source order.

<Typography
  level="bodyLarge"
  style={{
    color: "var(--sb-secondary-text-color)",
    marginBottom: 32,
  }}
>
  This guide will show you how to use CSS @layer to safely integrate legacy UI components with Apollo UI, ensuring predictable styling behavior and smooth migration paths.
</Typography>

## What is CSS @layer?

CSS `@layer` is a modern CSS feature that allows you to explicitly control the cascade order of your stylesheets. Layers are applied in the order they are declared, with later layers taking precedence over earlier ones, regardless of specificity.

<div style={{ marginBottom: 32 }}>
  <ul style={{
    listStyle: "none",
    padding: 0,
    display: "grid",
    gap: 16,
    gridTemplateColumns: "repeat(auto-fit, minmax(280px, 1fr))"
  }}>
    <li style={{
      display: "flex",
      alignItems: "flex-start",
      gap: 12,
      padding: 16,
      borderRadius: 8,
      border: "1px solid var(--sb-ui-border-color)",
    }}>
      <Safety size={24} style={{ color: "#0154EE", flexShrink: 0, marginTop: 2 }} />
      <div>
        <strong>Predictable Cascade</strong>
        <br />
        <span style={{ color: "var(--sb-secondary-text-color)" }}>
          Control which styles take precedence without relying on specificity wars
        </span>
      </div>
    </li>
    <li style={{
      display: "flex",
      alignItems: "flex-start",
      gap: 12,
      padding: 16,
      borderRadius: 8,
      border: "1px solid var(--sb-ui-border-color)",
    }}>
      <FileText size={24} style={{ color: "#0154EE", flexShrink: 0, marginTop: 2 }} />
      <div>
        <strong>Clean Separation</strong>
        <br />
        <span style={{ color: "var(--sb-secondary-text-color)" }}>
          Isolate legacy styles from modern component styles
        </span>
      </div>
    </li>
    <li style={{
      display: "flex",
      alignItems: "flex-start",
      gap: 12,
      padding: 16,
      borderRadius: 8,
      border: "1px solid var(--sb-ui-border-color)",
    }}>
      <Container size={24} style={{ color: "#0154EE", flexShrink: 0, marginTop: 2 }} />
      <div>
        <strong>Migration Friendly</strong>
        <br />
        <span style={{ color: "var(--sb-secondary-text-color)" }}>
          Gradually migrate components without breaking existing styles
        </span>
      </div>
    </li>
  </ul>
</div>

## Basic @layer Syntax

The `@layer` rule allows you to declare named layers and assign styles to them:

```css
/* Declare layer order */
@layer legacy, base, components, utilities;

/* Assign styles to layers */
@layer legacy {
  .old-button {
    background: #ccc;
    padding: 8px 16px;
  }
}

@layer components {
  .apollo-button {
    background: var(--apollo-color-primary);
    padding: 12px 24px;
  }
}
```

## Recommended Layer Structure

For Apollo UI integration, we recommend this layer hierarchy:

```css
@layer legacy, reset, base, tokens, components, utilities, overrides;
```

<div style={{
  padding: 20,
  borderRadius: 8,
  border: "1px solid var(--sb-ui-border-color)",
  backgroundColor: "var(--sb-ui-background-color)",
  marginBottom: 24,
}}>
  <Typography level="titleMedium" style={{ marginBottom: 16, fontWeight: 600 }}>
    Layer Purposes
  </Typography>
  <ul style={{ margin: 0, paddingLeft: 20 }}>
    <li><strong>legacy</strong>: All existing/legacy component styles</li>
    <li><strong>reset</strong>: CSS resets and normalizations</li>
    <li><strong>base</strong>: Base HTML element styles</li>
    <li><strong>tokens</strong>: Design token CSS custom properties</li>
    <li><strong>components</strong>: Apollo UI component styles</li>
    <li><strong>utilities</strong>: Utility classes and helpers</li>
    <li><strong>overrides</strong>: Project-specific overrides (use sparingly)</li>
  </ul>
</div>

## Practical Implementation

### Step 1: Declare Your Layers

Add this at the top of your main CSS file or in a dedicated layers file:

```css
/* layers.css */
@layer legacy, reset, base, tokens, components, utilities, overrides;
```

### Step 2: Wrap Legacy Styles

Wrap all your existing legacy styles in the legacy layer:

```css
@layer legacy {
  /* All your existing styles go here */
  .legacy-header {
    background: #f0f0f0;
    padding: 20px;
    border-bottom: 1px solid #ddd;
  }
  
  .legacy-button {
    background: linear-gradient(to bottom, #fff, #e6e6e6);
    border: 1px solid #ccc;
    padding: 8px 16px;
    border-radius: 4px;
  }
  
  .legacy-form input {
    border: 2px solid #999;
    padding: 6px;
    font-size: 14px;
  }
}
```

### Step 3: Apollo UI Components

Apollo UI components automatically use the `components` layer, so they will take precedence over legacy styles:

```tsx
import { Button, Input, Typography } from "@apollo/ui"

function ModernSection() {
  return (
    <div>
      <Typography level="titleLarge">Modern Apollo UI Section</Typography>
      <Button variant="primary">Apollo Button</Button>
      <Input placeholder="Apollo Input" />
    </div>
  )
}
```

### Step 4: Mixed Legacy and Modern Components

You can safely mix legacy and modern components in the same page:

```tsx
function MixedUIPage() {
  return (
    <div>
      {/* Legacy component - styles from @layer legacy */}
      <div className="legacy-header">
        <h1>Legacy Header</h1>
        <button className="legacy-button">Legacy Button</button>
      </div>

      {/* Modern Apollo UI components - styles from @layer components */}
      <div>
        <Typography level="titleLarge">Modern Section</Typography>
        <Button variant="primary">Apollo Button</Button>
        <Input placeholder="Modern input" />
      </div>
    </div>
  )
}
```

## Advanced Patterns

### Conditional Layer Loading

Load legacy styles only when needed:

```css
/* Only load legacy layer for specific pages */
.page-with-legacy {
  @layer legacy {
    .old-component {
      /* legacy styles */
    }
  }
}
```

### Component-Specific Legacy Layers

Create sub-layers for different legacy component systems:

```css
@layer legacy.bootstrap, legacy.material, legacy.custom, components;

@layer legacy.bootstrap {
  .btn {
    /* Bootstrap button styles */
  }
}

@layer legacy.material {
  .mat-button {
    /* Material UI button styles */
  }
}
```

### Gradual Migration Strategy

Use layers to gradually migrate components:

```css
@layer legacy, migration, components;

/* Original legacy component */
@layer legacy {
  .old-card {
    border: 1px solid #ccc;
    padding: 16px;
  }
}

/* Transitional styles during migration */
@layer migration {
  .old-card.migrating {
    /* Styles that bridge legacy and modern */
    border-color: var(--apollo-color-border);
  }
}

/* Final Apollo UI component takes precedence */
@layer components {
  .apollo-card {
    /* Apollo UI card styles */
  }
}
```

## Best Practices

<div style={{
  padding: 20,
  borderRadius: 8,
  border: "1px solid #22c55e",
  backgroundColor: "#f0fdf4",
  marginBottom: 24,
}}>
  <Typography level="titleMedium" style={{ marginBottom: 16, fontWeight: 600, color: "#15803d" }}>
    ✅ Do
  </Typography>
  <ul style={{ margin: 0, paddingLeft: 20, color: "#166534" }}>
    <li>Declare all layers at the beginning of your CSS</li>
    <li>Use descriptive layer names that reflect their purpose</li>
    <li>Keep legacy styles isolated in the legacy layer</li>
    <li>Document your layer strategy for your team</li>
    <li>Use layers to create clear migration paths</li>
    <li>Test thoroughly when introducing layers to existing projects</li>
  </ul>
</div>

<div style={{
  padding: 20,
  borderRadius: 8,
  border: "1px solid #ef4444",
  backgroundColor: "#fef2f2",
  marginBottom: 24,
}}>
  <Typography level="titleMedium" style={{ marginBottom: 16, fontWeight: 600, color: "#dc2626" }}>
    ❌ Don't
  </Typography>
  <ul style={{ margin: 0, paddingLeft: 20, color: "#991b1b" }}>
    <li>Mix layered and non-layered styles in the same stylesheet</li>
    <li>Use `!important` to override layer precedence</li>
    <li>Create too many layers (keep it simple)</li>
    <li>Change layer order frequently</li>
    <li>Put Apollo UI component overrides in the legacy layer</li>
  </ul>
</div>

## Browser Support

CSS `@layer` is supported in all modern browsers:

- Chrome 99+
- Firefox 97+
- Safari 15.4+
- Edge 99+

For older browsers, consider using a PostCSS plugin or CSS-in-JS solution that can polyfill layer behavior.

## Troubleshooting

### Styles Not Applying

If your styles aren't applying as expected:

1. Check that layers are declared in the correct order
2. Verify that styles are properly wrapped in `@layer` blocks
3. Use browser dev tools to inspect the computed styles
4. Ensure you're not mixing layered and non-layered styles

### Legacy Styles Overriding Apollo UI

If legacy styles are still overriding Apollo UI components:

1. Confirm Apollo UI styles are in the `components` layer
2. Check that `components` comes after `legacy` in your layer declaration
3. Look for non-layered styles that might have higher specificity

## Migration Checklist

- [ ] Declare layer order at the top of your main CSS file
- [ ] Wrap all legacy styles in `@layer legacy`
- [ ] Verify Apollo UI components use the `components` layer
- [ ] Test mixed legacy/modern component pages
- [ ] Document layer strategy for your team
- [ ] Plan gradual migration of legacy components
- [ ] Set up browser testing for layer support

## Conclusion

CSS `@layer` provides a robust solution for managing the complexity of displaying legacy UI alongside Apollo UI components. By establishing clear layer boundaries, you can ensure predictable styling behavior during migration periods and create a smooth path toward full Apollo UI adoption.

The key is to start with a well-defined layer structure, isolate legacy styles appropriately, and use layers as a migration tool rather than a permanent solution. As you migrate components to Apollo UI, you can gradually remove styles from the legacy layer until it's no longer needed.
