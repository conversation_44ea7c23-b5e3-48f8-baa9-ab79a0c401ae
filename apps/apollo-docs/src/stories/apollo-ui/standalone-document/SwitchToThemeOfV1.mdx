import { DiffCodeBlock } from "../../standalone-document/migration-guide/DiffCodeBlock"

import { Meta } from "@storybook/addon-docs/blocks"


<Meta title="@apollo∕ui/Switch to Theme of V1" tags={["docs"]} />

# Switch to Theme of V1

If you don't want the style to change after upgrade, we have provided a `legacy layer` rule that can restore v1 (legacy) style. 

you can learn more about legacy and another layers in [CSS Layers](?path=/docs/apollo∕ui-theming-css-layers--docs)

## Usage

### Step 1: Legacy Theme Setup from @apollo/ui

Apollo legacy components require a theme provider to work correctly. Wrap your app with the ThemeProvider and create a theme:

```jsx title="App.js"
import React from 'react';
import { ThemeProvider, createTheme } from "@apollo/ui"

const appTheme = createTheme()

function App({ children }) {
  return (
    <ThemeProvider theme={appTheme}>
      {children}
    </ThemeProvider>
  )
}
```

### Step 2: Declare Your Layers

Add this at the top of your main CSS file or in a dedicated layers file:

```css
@layer reset, base, legacy;
```

If you need to customize the legacy style only, you can Wrap all your existing legacy styles in the legacy layer:

```css
@layer legacy {
  /* All your existing styles go here */
}
```

Note: If you don't want to use legacy style anymore, you can remove layers declaration.
But if you want to use CSS layer, you can set to declare default layers 
<DiffCodeBlock
  code={`- @layer reset, base, legacy;
+ @layer reset, base, legacy, apollo;`}
/>
